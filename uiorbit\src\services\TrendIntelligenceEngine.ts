import * as vscode from 'vscode';

import * as path from 'path';

import * as fs from 'fs-extra';

import {
  TrendAnalysis,
  TrendPattern,
  TechnologyTrend,
  TrendRecommendation,
  WorkspaceContext,
  ModernizationSuggestion,
  PatternAnalysis,
  TrendingDesignTokens,
  TrendAwareComponent,
  TrendDatabase,
  CurrentTrend,
  ComponentInfo
} from '../types/ContextTypes';
import { Logger } from '../utils/Logger';

import { AIService } from './AIService';
import { FrontendContextEngine } from './FrontendContextEngine';

/**
 * Trend Intelligence Engine
 * Analyzes current UI/UX trends and provides intelligent design suggestions
 */
export class TrendIntelligenceEngine {
  private trendDatabase: TrendDatabase | null = null;
  private lastTrendUpdate: Date | null = null;
  private readonly TREND_UPDATE_INTERVAL = 7 * 24 * 60 * 60 * 1000; // 7 days

  constructor(
    private aiService: AIService,
    private contextEngine: FrontendContextEngine,
    private context: vscode.ExtensionContext
  ) {}

  /**
   * Initialize the trend intelligence engine
   */
  async initialize(): Promise<void> {
    Logger.info('Initializing Trend Intelligence Engine...');

    try {
      // Load existing trend database
      await this.loadTrendDatabase();

      // Update trends if needed
      if (this.shouldUpdateTrends()) {
        await this.updateTrendDatabase();
      }

      Logger.info('Trend Intelligence Engine initialized successfully');
    } catch (error) {
      Logger.error('Failed to initialize Trend Intelligence Engine:', error);
      throw error;
    }
  }

  /**
   * Analyze current project and provide trend-based recommendations
   */
  async analyzeProjectTrends(context?: WorkspaceContext): Promise<TrendAnalysis> {
    Logger.info('Analyzing project trends...');

    try {
      // Get workspace context if not provided
      const workspaceContext = context || await this.contextEngine.analyzeWorkspace();

      // Analyze current trends in the project
      const currentTrends = await this.identifyCurrentTrends(workspaceContext);

      // Get trending technologies and patterns
      const trendingTechnologies = await this.getTrendingTechnologies();
      const trendingPatterns = await this.getTrendingPatterns();

      // Generate recommendations
      const recommendations = await this.generateTrendRecommendations(
        workspaceContext,
        currentTrends,
        trendingTechnologies,
        trendingPatterns
      );

      return {
        patterns: trendingPatterns,
        technologies: trendingTechnologies,
        recommendations,
        lastUpdated: new Date()
      };

    } catch (error) {
      Logger.error('Failed to analyze project trends:', error);
      throw error;
    }
  }

  /**
   * Get intelligent suggestions for modern UI/UX improvements
   */
  async getModernizationSuggestions(component?: string): Promise<ModernizationSuggestion[]> {
    Logger.info('Generating modernization suggestions...');

    try {
      const workspaceContext = await this.contextEngine.analyzeWorkspace();
      const currentTrends = await this.getCurrentTrends();

      const prompt = `
Analyze this ${workspaceContext.framework.name} project and provide modern UI/UX improvement suggestions:

Project Context:
- Framework: ${workspaceContext.framework.name}
- Styling: ${workspaceContext.styles.framework}
- Components: ${workspaceContext.components.length}
- Current patterns: ${workspaceContext.patterns.map(p => p.name).join(', ')}

${component ? `Specific component: ${component}` : 'General project analysis'}

Current 2024 UI/UX Trends to consider:
- Glassmorphism and frosted glass effects
- Neumorphism with subtle shadows
- Dark mode with proper contrast
- Micro-interactions and smooth animations
- Accessibility-first design
- Mobile-first responsive design
- Performance-optimized animations
- Modern color palettes (gradients, vibrant colors)
- Typography with proper hierarchy
- Minimalist design with purposeful whitespace

Provide JSON response with modernization suggestions:
{
  "suggestions": [
    {
      "category": "design|animation|accessibility|performance|ux",
      "title": "Suggestion title",
      "description": "Detailed description",
      "impact": "high|medium|low",
      "effort": "high|medium|low",
      "trendAlignment": "glassmorphism|neumorphism|dark-mode|micro-interactions",
      "implementation": "Specific implementation steps",
      "codeExample": "Code example if applicable",
      "benefits": ["benefit1", "benefit2"]
    }
  ]
}

Focus on actionable, modern improvements that align with current trends.
`;

      const response = await this.aiService.processMessage(prompt);
      const parsed = JSON.parse(response.content);

      return parsed.suggestions.map((s: any) => ({
        ...s,
        id: this.generateSuggestionId(s.title),
        priority: this.calculatePriority(s.impact, s.effort),
        trendscore: this.calculateTrendScore(s.trendAlignment)
      }));

    } catch (error) {
      Logger.error('Failed to generate modernization suggestions:', error);
      return [];
    }
  }

  /**
   * Analyze design patterns and suggest modern alternatives
   */
  async analyzeDesignPatterns(): Promise<PatternAnalysis[]> {
    Logger.info('Analyzing design patterns...');

    try {
      const workspaceContext = await this.contextEngine.analyzeWorkspace();
      const patterns: PatternAnalysis[] = [];

      // Analyze component patterns
      for (const component of workspaceContext.components) {
        const analysis = await this.analyzeComponentPattern(component);
        if (analysis) {
          patterns.push(analysis);
        }
      }

      // Analyze style patterns
      const styleAnalysis = await this.analyzeStylePatterns(workspaceContext.styles);
      patterns.push(...styleAnalysis);

      return patterns;

    } catch (error) {
      Logger.error('Failed to analyze design patterns:', error);
      return [];
    }
  }

  /**
   * Get trending color palettes and design tokens
   */
  async getTrendingDesignTokens(): Promise<TrendingDesignTokens> {
    Logger.info('Getting trending design tokens...');

    const prompt = `
Provide current trending design tokens for 2024:

Return JSON with:
{
  "colorPalettes": [
    {
      "name": "Palette name",
      "theme": "modern|vibrant|minimal|dark",
      "colors": {
        "primary": "#hex",
        "secondary": "#hex",
        "accent": "#hex",
        "background": "#hex",
        "surface": "#hex",
        "text": "#hex"
      },
      "usage": "When to use this palette"
    }
  ],
  "typography": [
    {
      "name": "Typography style",
      "fontFamily": "Font family",
      "weights": [400, 500, 600, 700],
      "sizes": {
        "xs": "12px",
        "sm": "14px",
        "base": "16px",
        "lg": "18px",
        "xl": "20px",
        "2xl": "24px",
        "3xl": "30px",
        "4xl": "36px"
      },
      "lineHeights": {
        "tight": "1.25",
        "normal": "1.5",
        "relaxed": "1.75"
      }
    }
  ],
  "spacing": {
    "xs": "4px",
    "sm": "8px",
    "md": "16px",
    "lg": "24px",
    "xl": "32px",
    "2xl": "48px",
    "3xl": "64px"
  },
  "borderRadius": {
    "sm": "4px",
    "md": "8px",
    "lg": "12px",
    "xl": "16px",
    "2xl": "24px",
    "full": "9999px"
  },
  "shadows": [
    {
      "name": "subtle",
      "value": "0 1px 3px rgba(0,0,0,0.1)"
    },
    {
      "name": "medium",
      "value": "0 4px 6px rgba(0,0,0,0.1)"
    },
    {
      "name": "large",
      "value": "0 10px 15px rgba(0,0,0,0.1)"
    }
  ]
}

Focus on modern, accessible, and trending design tokens for 2024.
`;

    try {
      const response = await this.aiService.processMessage(prompt);
      return JSON.parse(response.content);
    } catch (error) {
      Logger.error('Failed to get trending design tokens:', error);
      return this.getDefaultDesignTokens();
    }
  }

  /**
   * Generate trend-aware component suggestions
   */
  async generateTrendAwareComponent(description: string): Promise<TrendAwareComponent> {
    Logger.info(`Generating trend-aware component: ${description}`);

    const currentTrends = await this.getCurrentTrends();
    const designTokens = await this.getTrendingDesignTokens();

    const prompt = `
Generate a modern, trend-aware component based on: "${description}"

Apply these current 2024 trends:
${currentTrends.map(t => `- ${t.name}: ${t.description}`).join('\n')}

Use these trending design tokens:
- Colors: Modern palettes with proper contrast
- Typography: Clean, readable fonts with proper hierarchy
- Spacing: Consistent spacing system
- Animations: Smooth, purposeful micro-interactions
- Accessibility: WCAG AA compliance

Generate a React component with:
1. Modern design following current trends
2. GSAP animations for micro-interactions
3. Tailwind CSS for styling
4. Full accessibility support
5. Responsive design
6. TypeScript interfaces

Provide JSON response:
{
  "component": {
    "name": "ComponentName",
    "code": "Complete React component code",
    "styles": "Tailwind classes and custom CSS",
    "animations": "GSAP animation code",
    "props": "TypeScript interface",
    "usage": "Usage example"
  },
  "trendsApplied": ["trend1", "trend2"],
  "modernFeatures": ["feature1", "feature2"],
  "accessibilityFeatures": ["aria-labels", "keyboard-nav"]
}
`;

    try {
      const response = await this.aiService.processMessage(prompt);
      const parsed = JSON.parse(response.content);
      
      return {
        ...parsed.component,
        trendsApplied: parsed.trendsApplied,
        modernFeatures: parsed.modernFeatures,
        accessibilityFeatures: parsed.accessibilityFeatures,
        trendScore: this.calculateComponentTrendScore(parsed.trendsApplied)
      };

    } catch (error) {
      Logger.error('Failed to generate trend-aware component:', error);
      throw error;
    }
  }

  /**
   * Load trend database from storage
   */
  private async loadTrendDatabase(): Promise<void> {
    try {
      const dbPath = path.join(this.context.globalStorageUri.fsPath, 'trends.json');
      
      if (await fs.pathExists(dbPath)) {
        const data = await fs.readJson(dbPath);
        this.trendDatabase = data;
        this.lastTrendUpdate = new Date(data.lastUpdated);
        Logger.info('Loaded existing trend database');
      } else {
        // Initialize with default trends
        this.trendDatabase = await this.getDefaultTrendDatabase();
        await this.saveTrendDatabase();
        Logger.info('Initialized default trend database');
      }
    } catch (error) {
      Logger.error('Failed to load trend database:', error);
      this.trendDatabase = await this.getDefaultTrendDatabase();
    }
  }

  /**
   * Update trend database with latest trends
   */
  private async updateTrendDatabase(): Promise<void> {
    Logger.info('Updating trend database...');

    try {
      // Get latest trends from AI analysis
      const latestTrends = await this.fetchLatestTrends();
      
      // Update database
      this.trendDatabase = {
        patterns: latestTrends.patterns || this.trendDatabase?.patterns || [],
        technologies: latestTrends.technologies || this.trendDatabase?.technologies || [],
        designTokens: latestTrends.designTokens || this.trendDatabase?.designTokens || this.getDefaultDesignTokens(),
        lastUpdated: new Date().toISOString()
      };

      // Save to storage
      await this.saveTrendDatabase();
      this.lastTrendUpdate = new Date();

      Logger.info('Trend database updated successfully');
    } catch (error) {
      Logger.error('Failed to update trend database:', error);
    }
  }

  /**
   * Check if trends should be updated
   */
  private shouldUpdateTrends(): boolean {
    if (!this.lastTrendUpdate) return true;
    
    const timeSinceUpdate = Date.now() - this.lastTrendUpdate.getTime();
    return timeSinceUpdate > this.TREND_UPDATE_INTERVAL;
  }

  /**
   * Save trend database to storage
   */
  private async saveTrendDatabase(): Promise<void> {
    try {
      const dbPath = path.join(this.context.globalStorageUri.fsPath, 'trends.json');
      await fs.ensureDir(path.dirname(dbPath));
      await fs.writeJson(dbPath, this.trendDatabase, { spaces: 2 });
    } catch (error) {
      Logger.error('Failed to save trend database:', error);
    }
  }

  /**
   * Identify current trends in the project
   */
  private async identifyCurrentTrends(context: WorkspaceContext): Promise<CurrentTrend[]> {
    const trends: CurrentTrend[] = [];

    // Analyze framework trends
    if (context.framework.name === 'React') {
      trends.push({
        name: 'React Hooks',
        description: 'Modern React development with hooks',
        popularity: 0.9,
        category: 'framework'
      });
    }

    // Analyze styling trends
    if (context.styles.framework === 'Tailwind CSS') {
      trends.push({
        name: 'Utility-First CSS',
        description: 'Rapid development with utility classes',
        popularity: 0.85,
        category: 'styling'
      });
    }

    // Analyze animation trends
    const hasGSAP = context.dependencies.production['gsap'] ||
                   context.dependencies.development['gsap'];
    if (hasGSAP) {
      trends.push({
        name: 'GSAP Animations',
        description: 'Professional animations with GSAP',
        popularity: 0.8,
        category: 'animation'
      });
    }

    return trends;
  }

  /**
   * Get trending technologies
   */
  private async getTrendingTechnologies(): Promise<TechnologyTrend[]> {
    return [
      {
        name: 'Vite',
        category: 'tool',
        popularity: 0.9,
        growth: 0.3,
        description: 'Fast build tool for modern web development',
        alternatives: ['Webpack', 'Parcel', 'Rollup'],
        migrationPath: 'Migrate from Create React App to Vite for better performance'
      },
      {
        name: 'Tailwind CSS',
        category: 'library',
        popularity: 0.85,
        growth: 0.25,
        description: 'Utility-first CSS framework',
        alternatives: ['styled-components', 'Emotion', 'CSS Modules'],
        migrationPath: 'Replace custom CSS with Tailwind utility classes'
      },
      {
        name: 'GSAP',
        category: 'library',
        popularity: 0.8,
        growth: 0.2,
        description: 'Professional animation library',
        alternatives: ['Framer Motion', 'CSS Animations', 'Lottie'],
        migrationPath: 'Enhance existing animations with GSAP for better performance'
      }
    ];
  }

  /**
   * Get trending patterns
   */
  private async getTrendingPatterns(): Promise<TrendPattern[]> {
    return [
      {
        name: 'Glassmorphism',
        category: 'design',
        popularity: 0.75,
        adoption: 'growing',
        description: 'Frosted glass effect with transparency and blur',
        examples: ['backdrop-filter: blur()', 'semi-transparent backgrounds'],
        benefits: ['Modern aesthetic', 'Depth perception', 'Visual hierarchy'],
        considerations: ['Browser support', 'Performance impact', 'Accessibility']
      },
      {
        name: 'Micro-interactions',
        category: 'design',
        popularity: 0.9,
        adoption: 'mainstream',
        description: 'Small animations that provide feedback',
        examples: ['Button hover effects', 'Loading states', 'Form validation'],
        benefits: ['Better UX', 'User engagement', 'Visual feedback'],
        considerations: ['Performance', 'Accessibility', 'Overuse']
      },
      {
        name: 'Dark Mode',
        category: 'design',
        popularity: 0.85,
        adoption: 'mainstream',
        description: 'Dark color scheme for better user experience',
        examples: ['System preference detection', 'Toggle switches', 'Proper contrast'],
        benefits: ['Reduced eye strain', 'Battery saving', 'Modern look'],
        considerations: ['Color contrast', 'Accessibility', 'Brand consistency']
      }
    ];
  }

  /**
   * Generate trend recommendations
   */
  private async generateTrendRecommendations(
    context: WorkspaceContext,
    currentTrends: CurrentTrend[],
    technologies: TechnologyTrend[],
    patterns: TrendPattern[]
  ): Promise<TrendRecommendation[]> {
    const recommendations: TrendRecommendation[] = [];

    // Recommend trending technologies not in use
    for (const tech of technologies) {
      const isUsed = this.isTechnologyUsed(tech, context);
      if (!isUsed && tech.popularity > 0.7) {
        recommendations.push({
          type: 'adopt',
          technology: tech.name,
          reasoning: `${tech.name} is trending with ${Math.round(tech.popularity * 100)}% popularity and ${Math.round(tech.growth * 100)}% growth`,
          impact: tech.popularity > 0.8 ? 'high' : 'medium',
          effort: this.estimateAdoptionEffort(tech, context),
          timeline: this.estimateTimeline(tech, context)
        });
      }
    }

    // Recommend trending patterns
    for (const pattern of patterns) {
      if (pattern.adoption === 'growing' || pattern.adoption === 'mainstream') {
        recommendations.push({
          type: 'trial',
          technology: pattern.name,
          reasoning: `${pattern.name} is a ${pattern.adoption} design pattern that can improve user experience`,
          impact: 'medium',
          effort: 'low',
          timeline: '1-2 weeks'
        });
      }
    }

    return recommendations;
  }

  /**
   * Analyze component pattern
   */
  private async analyzeComponentPattern(component: ComponentInfo): Promise<PatternAnalysis | null> {
    // Simple pattern analysis based on component structure
    const analysis: PatternAnalysis = {
      componentName: component.name,
      currentPattern: this.identifyComponentPattern(component),
      modernAlternatives: this.getModernAlternatives(component),
      trendAlignment: this.calculateTrendAlignment(component),
      suggestions: this.generatePatternSuggestions(component)
    };

    return analysis;
  }

  /**
   * Analyze style patterns
   */
  private async analyzeStylePatterns(styles: any): Promise<PatternAnalysis[]> {
    // Analyze CSS patterns and suggest modern alternatives
    return [
      {
        componentName: 'Global Styles',
        currentPattern: styles.framework || 'Custom CSS',
        modernAlternatives: ['Tailwind CSS', 'CSS-in-JS', 'CSS Modules'],
        trendAlignment: styles.framework === 'Tailwind CSS' ? 0.9 : 0.3,
        suggestions: [
          'Consider adopting utility-first CSS approach',
          'Implement design system with consistent tokens',
          'Add dark mode support'
        ]
      }
    ];
  }

  /**
   * Get current trends from database
   */
  private async getCurrentTrends(): Promise<CurrentTrend[]> {
    return [
      {
        name: 'Glassmorphism',
        description: 'Frosted glass effect with backdrop blur',
        popularity: 0.75,
        category: 'design'
      },
      {
        name: 'Micro-interactions',
        description: 'Subtle animations for user feedback',
        popularity: 0.9,
        category: 'animation'
      },
      {
        name: 'Dark Mode',
        description: 'Dark color schemes with proper contrast',
        popularity: 0.85,
        category: 'design'
      }
    ];
  }

  /**
   * Fetch latest trends from AI analysis
   */
  private async fetchLatestTrends(): Promise<Partial<TrendDatabase>> {
    const prompt = `
Analyze the latest UI/UX trends for 2024 and provide a comprehensive update:

Provide JSON response with:
{
  "patterns": [
    {
      "name": "Pattern name",
      "category": "design|architecture|performance|accessibility",
      "popularity": 0.8,
      "adoption": "emerging|growing|mainstream|declining",
      "description": "Pattern description",
      "examples": ["example1", "example2"],
      "benefits": ["benefit1", "benefit2"],
      "considerations": ["consideration1", "consideration2"]
    }
  ],
  "technologies": [
    {
      "name": "Technology name",
      "category": "framework|library|tool|language",
      "popularity": 0.9,
      "growth": 0.2,
      "description": "Technology description",
      "alternatives": ["alt1", "alt2"],
      "migrationPath": "Migration guidance"
    }
  ]
}

Focus on current, actionable trends for frontend development.
`;

    try {
      const response = await this.aiService.processMessage(prompt);
      return JSON.parse(response.content);
    } catch (error) {
      Logger.error('Failed to fetch latest trends:', error);
      return {};
    }
  }

  /**
   * Get default trend database
   */
  private async getDefaultTrendDatabase(): Promise<TrendDatabase> {
    return {
      patterns: await this.getTrendingPatterns(),
      technologies: await this.getTrendingTechnologies(),
      designTokens: this.getDefaultDesignTokens(),
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Get default design tokens
   */
  private getDefaultDesignTokens(): TrendingDesignTokens {
    return {
      colorPalettes: [
        {
          name: 'Modern Blue',
          theme: 'modern',
          colors: {
            primary: '#3B82F6',
            secondary: '#6366F1',
            accent: '#8B5CF6',
            background: '#FFFFFF',
            surface: '#F8FAFC',
            text: '#1E293B'
          },
          usage: 'Professional applications and dashboards'
        }
      ],
      typography: [
        {
          name: 'Modern Sans',
          fontFamily: 'Inter, system-ui, sans-serif',
          weights: [400, 500, 600, 700],
          sizes: {
            xs: '12px',
            sm: '14px',
            base: '16px',
            lg: '18px',
            xl: '20px',
            '2xl': '24px',
            '3xl': '30px',
            '4xl': '36px'
          },
          lineHeights: {
            tight: '1.25',
            normal: '1.5',
            relaxed: '1.75'
          }
        }
      ],
      spacing: {
        xs: '4px',
        sm: '8px',
        md: '16px',
        lg: '24px',
        xl: '32px',
        '2xl': '48px',
        '3xl': '64px'
      },
      borderRadius: {
        sm: '4px',
        md: '8px',
        lg: '12px',
        xl: '16px',
        '2xl': '24px',
        full: '9999px'
      },
      shadows: [
        {
          name: 'subtle',
          value: '0 1px 3px rgba(0,0,0,0.1)'
        },
        {
          name: 'medium',
          value: '0 4px 6px rgba(0,0,0,0.1)'
        },
        {
          name: 'large',
          value: '0 10px 15px rgba(0,0,0,0.1)'
        }
      ]
    };
  }

  // Utility methods
  private generateSuggestionId(title: string): string {
    return title.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
  }

  private calculatePriority(impact: string, effort: string): number {
    const impactScore = impact === 'high' ? 3 : impact === 'medium' ? 2 : 1;
    const effortScore = effort === 'low' ? 3 : effort === 'medium' ? 2 : 1;
    return impactScore * effortScore;
  }

  private calculateTrendScore(trendAlignment: string): number {
    const trendScores: Record<string, number> = {
      'glassmorphism': 0.75,
      'neumorphism': 0.6,
      'dark-mode': 0.85,
      'micro-interactions': 0.9
    };
    return trendScores[trendAlignment] || 0.5;
  }

  private calculateComponentTrendScore(trends: string[]): number {
    return trends.reduce((sum, trend) => sum + this.calculateTrendScore(trend), 0) / trends.length;
  }

  private isTechnologyUsed(tech: TechnologyTrend, context: WorkspaceContext): boolean {
    const deps = { ...context.dependencies.production, ...context.dependencies.development };
    return Object.keys(deps).some(dep =>
      dep.toLowerCase().includes(tech.name.toLowerCase()) ||
      tech.name.toLowerCase().includes(dep.toLowerCase())
    );
  }

  private estimateAdoptionEffort(tech: TechnologyTrend, context: WorkspaceContext): 'low' | 'medium' | 'high' {
    // Simple heuristic based on technology category and current stack
    if (tech.category === 'tool') return 'medium';
    if (tech.category === 'library') return 'low';
    return 'high';
  }

  private estimateTimeline(tech: TechnologyTrend, context: WorkspaceContext): string {
    const effort = this.estimateAdoptionEffort(tech, context);
    switch (effort) {
      case 'low': return '1-2 weeks';
      case 'medium': return '2-4 weeks';
      case 'high': return '1-2 months';
      default: return '2-4 weeks';
    }
  }

  private identifyComponentPattern(component: ComponentInfo): string {
    // Simple pattern identification
    if (component.type === 'functional') return 'Functional Component';
    if (component.type === 'class') return 'Class Component';
    return 'Unknown Pattern';
  }

  private getModernAlternatives(component: ComponentInfo): string[] {
    if (component.type === 'class') {
      return ['Functional Component with Hooks', 'Custom Hooks', 'Context API'];
    }
    return ['Optimized with React.memo', 'Custom Hooks', 'Compound Components'];
  }

  private calculateTrendAlignment(component: ComponentInfo): number {
    // Calculate how well the component aligns with current trends
    let score = 0.5; // Base score

    if (component.type === 'functional') score += 0.3;
    if (component.props.length > 0) score += 0.1;
    if (component.name.includes('Hook')) score += 0.2;

    return Math.min(1.0, score);
  }

  private generatePatternSuggestions(component: ComponentInfo): string[] {
    const suggestions: string[] = [];

    if (component.type === 'class') {
      suggestions.push('Convert to functional component with hooks');
    }

    if (component.complexity > 10) {
      suggestions.push('Consider breaking into smaller components');
    }

    suggestions.push('Add TypeScript interfaces for better type safety');
    suggestions.push('Implement proper error boundaries');

    return suggestions;
  }
}
