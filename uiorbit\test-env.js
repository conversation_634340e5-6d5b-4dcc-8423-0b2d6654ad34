const fs = require('fs');
const path = require('path');

// Test .env file reading
async function testEnvReading() {
    console.log('Testing .env file reading...');
    
    const possiblePaths = [
        path.join(__dirname, '.env'),
        path.join(process.cwd(), '.env'),
        path.join(process.cwd(), 'uiorbit', '.env'),
    ];
    
    console.log('Current working directory:', process.cwd());
    console.log('__dirname:', __dirname);
    
    for (const envPath of possiblePaths) {
        console.log(`\nChecking: ${envPath}`);
        try {
            if (fs.existsSync(envPath)) {
                console.log('✅ File exists');
                const content = fs.readFileSync(envPath, 'utf-8');
                console.log(`Content length: ${content.length}`);
                
                // Parse the content
                const lines = content.split('\n');
                const envConfig = {};
                
                for (const line of lines) {
                    const trimmedLine = line.trim();
                    if (!trimmedLine || trimmedLine.startsWith('#')) {
                        continue;
                    }
                    
                    const equalIndex = trimmedLine.indexOf('=');
                    if (equalIndex > 0) {
                        const key = trimmedLine.substring(0, equalIndex).trim();
                        const value = trimmedLine.substring(equalIndex + 1).trim();
                        const cleanValue = value.replace(/^["']|["']$/g, '');
                        envConfig[key] = cleanValue;
                    }
                }
                
                console.log(`Parsed ${Object.keys(envConfig).length} variables`);
                console.log('Variables:', Object.keys(envConfig).join(', '));
                
                if (envConfig.OPENAI_API_KEY) {
                    console.log(`✅ OPENAI_API_KEY found: ${envConfig.OPENAI_API_KEY.substring(0, 10)}...`);
                } else {
                    console.log('❌ OPENAI_API_KEY not found');
                }
                
                return;
            } else {
                console.log('❌ File does not exist');
            }
        } catch (error) {
            console.log('❌ Error:', error.message);
        }
    }
    
    console.log('\n❌ No .env file found in any location');
}

testEnvReading().catch(console.error);
